import TradesTable from './components/TradesTable'
import TradeForm from './components/TradeForm'

import MoneyManagementStatus from './components/MoneyManagementStatus'

function App(): React.JSX.Element {
  return (
    <main className="flex flex-col relative items-center justify-center">
      <div className="flex gap-2 justify-start w-full p-2">
        <div className="flex flex-col gap-2 flex-1">
          <TradesTable />
          <MoneyManagementStatus />
        </div>

        <div className="flex flex-col gap-2 w-[160px]">
          <TradeForm />
        </div>
      </div>
    </main>
  )
}

export default App
