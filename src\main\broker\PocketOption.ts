import { io, Socket } from 'socket.io-client'
import { Logger } from '../../shared/utils/Logger'
import { extractAuth } from '../../shared/utils/auth'
import { BrowserWindow } from 'electron'
import {
  expiryToSeconds,
  formatAssets,
  formatChartTimeframe,
  formatData,
  timeframeToOffset,
  toMs
} from '../../shared/utils/formatters'
import MoneyManager from '../core/MoneyManager'
import { SignalEngine } from '../core/SignalEngine'
import { StrategyFactory } from '../core/StrategyFactory'

const logger = Logger.getContextLogger('SOCKET')

export class PocketOption {
  public static instance: PocketOption | null = null
  private socket: Socket | null = null
  private state: BrokerState = 'disconnected'
  private heartbeatInterval: NodeJS.Timeout | null = null
  private chartSettings: ChartInfo | null = null
  private accountBalance: number = 0
  private lastSignalBarTime: number | null = null

  private isBotRunning: boolean = false
  private tradeSettings: TradeSettings | null = null

  private moneyManager: MoneyManager | null = null
  private signalEngine: SignalEngine | null = null
  private buckets: Map<string, Candle> = new Map()

  private constructor(
    private ssID: string,
    private isDemo: boolean
  ) {
    this.connect()
  }

  public static getInstance(ssID: string, isDemo: boolean): PocketOption {
    if (!PocketOption.instance) {
      PocketOption.instance = new PocketOption(ssID, isDemo)
    }
    return PocketOption.instance
  }

  async connect(): Promise<void> {
    if (this.socket && this.isConnected()) return

    try {
      this.setState('connecting')
      logger.info('Connecting to PocketOption...')

      this.socket = io(`wss://demo-api-eu.po.market`, {
        transports: ['websocket'],
        query: {
          EIO: '4',
          transport: ['websocket']
        },
        extraHeaders: {
          Origin: `https://pocketoption.com`
        },
        path: '/socket.io/'
      })

      this.setupEventListeners()
    } catch (error) {
      logger.error(`Failed to connect to PocketOption: ${error}`)
    }
  }

  async disconnect(): Promise<void> {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.setState('disconnected')
    this.stopHeartbeat()
  }

  async startBot(): Promise<void> {
    try {
      if (!this.socket || !this.isConnected()) {
        throw new Error('Socket not connected to the server')
      }

      if (this.getState() !== 'authenticated') {
        throw new Error('Not authenticated with the server')
      }

      if (!this.tradeSettings) {
        throw new Error('Trade settings not set')
      }

      if (!this.chartSettings) {
        throw new Error('Chart settings not set')
      }

      if (this.isBotRunning) return

      // Validate trade capital before starting
      if (this.tradeSettings.tradeCapital && this.tradeSettings.targetProfit) {
        const balance = this.accountBalance
        this.moneyManager = new MoneyManager(balance)

        const validation = this.moneyManager.validateTradeCapital(this.tradeSettings.tradeCapital)
        if (!validation.isValid) {
          throw new Error(validation.error)
        }

        // Start money management session
        this.moneyManager.startSession(
          this.tradeSettings.tradeCapital,
          this.tradeSettings.targetProfit,
          this.tradeSettings.tradeAmount || 10,
          this.tradeSettings.strategy || 'martingale'
        )

        logger.info(
          `Money management session started: Capital: $${this.tradeSettings.tradeCapital}, Target: $${this.tradeSettings.targetProfit}`
        )
      } else {
        throw new Error('Trade capital and target profit must be set')
      }

      logger.debug(`Starting bot...`)

      // 1. Select asset to trigger `updateStream` event
      await this.selectAsset(
        this.chartSettings.settings.symbol,
        this.chartSettings.settings.chartPeriod
      )
      // Then this will trigger `loadHistoryPeriodFast` event
      await this.loadHistoryPeriod()

      // 2. Build strategies via StrategyFactory
      const strategyMeta = StrategyFactory.getAvailableStrategies()
      const strategies = strategyMeta.map((s) => ({ name: s.type }) as const)

      logger.debug(`Strategies: ${strategies.map((s) => s.name).join(', ')}`)

      // 3. Spin up the signal engine
      const periodName = formatChartTimeframe(this.chartSettings.settings.chartPeriod)
      const candlePeriodSeconds = expiryToSeconds(periodName)

      logger.debug(
        `Chart period: ${this.chartSettings.settings.chartPeriod} → ${periodName} → ${candlePeriodSeconds}s`
      )

      this.signalEngine = new SignalEngine(strategies, {
        ...this.tradeSettings,

        // Only put this if you want a user-defined expiry
        expirySeconds: this.tradeSettings.expiry
          ? expiryToSeconds(this.tradeSettings.expiry)
          : undefined,

        candlePeriodSeconds,
        riskTolerance: 0.002
      })

      this.isBotRunning = true
      this.broadcast('bot:started')

      // Broadcast initial money management status
      if (this.moneyManager) {
        const status = this.moneyManager.getSessionStatus()
        this.broadcast('money-management:status', status)
      }
    } catch (error) {
      const message = this.getErrorMessage(error)
      logger.error(`Failed to start bot: ${message}`)
      throw error // Re-throw to allow UI to handle the error
    }
  }

  async stopBot(): Promise<void> {
    this.isBotRunning = false

    // End money management session and get results
    if (this.moneyManager && this.moneyManager.isSessionActive()) {
      const sessionResult = this.moneyManager.getSessionResult()
      this.moneyManager.endSession()

      if (sessionResult) {
        this.broadcast('money-management:session-ended', sessionResult)
        logger.info(
          `Trading session ended: ${sessionResult.profitable ? 'Profitable' : 'Loss'} - Total: $${sessionResult.totalProfit.toFixed(2)}`
        )
      }
    }

    this.broadcast('bot:stopped')
  }

  async setTradeSettings(settings: TradeSettings): Promise<void> {
    this.tradeSettings = { ...settings }
  }

  async placeOrder(signal?: Signal): Promise<void> {
    if (!this.socket || !this.isConnected()) return

    try {
      if (!this.chartSettings) {
        throw new Error('Chart settings not set')
      }

      if (!this.tradeSettings) {
        throw new Error('Trade settings not set')
      }

      // Check if money management session should stop
      if (this.moneyManager && this.moneyManager.isSessionActive()) {
        const stopCheck = this.moneyManager.shouldStopSession()
        if (stopCheck.shouldStop) {
          logger.warn(`Stopping bot: ${stopCheck.reason}`)
          await this.stopBot()
          return
        }
      }

      // Get trade amount from money manager if available
      let tradeAmount = this.tradeSettings.tradeAmount as number
      if (this.moneyManager && this.moneyManager.isSessionActive()) {
        const session = this.moneyManager.getCurrentSession()
        if (session) {
          tradeAmount = session.currentTradeAmount
        }
      }

      if (signal && signal.shouldTrade) {
        const payload: OpenOrderPayload = {
          asset: this.chartSettings.settings.symbol,
          amount: tradeAmount,
          action: signal.action === 'BUY' ? 'call' : 'put',
          time: signal.expirySeconds ?? 30,
          isDemo: this.isDemo ? 1 : 0,
          optionType: 100,
          requestId: Math.floor(Date.now() / 1000)
        }

        this.socket.emit('openOrder', payload)
        logger.success(`Placing order: ${JSON.stringify(payload, null, 2)}`)
      } else {
        logger.warn(`Not placing order: ${signal ? signal.reason : 'No signal provided'}`)
        throw new Error(`Not placing order: ${signal ? signal.reason : 'No signal provided'}`)
      }
    } catch (error) {
      const message = this.getErrorMessage(error)
      logger.error(`Failed to place order: ${message}`)
    }
  }

  protected async selectAsset(assetSymbol: string, chartPeriod: number): Promise<void> {
    if (!this.socket || !this.isConnected()) return

    try {
      this.socket.emit('changeSymbol', {
        asset: assetSymbol,
        period: chartPeriod
      })

      this.socket.emit('unsubfor', assetSymbol)
      this.socket.emit('subfor', assetSymbol)
    } catch (error) {
      const message = this.getErrorMessage(error)
      logger.error(`Failed to select asset: ${message}`)
    }
  }

  protected getChartSettings(): ChartInfo | null {
    return this.chartSettings
  }

  protected setupEventListeners(): void {
    if (!this.socket) return

    this.socket.on('connect', this.handleConnect.bind(this))
    this.socket.on('disconnect', this.handleDisconnect.bind(this))
    this.socket.on('error', this.handleError.bind(this))

    this.socket.on('successauth', this.handleSuccessAuth.bind(this))
    this.socket.on('successupdateBalance', this.handleUpdateBalance.bind(this))
    this.socket.on('successopenOrder', this.handleSuccessOpenOrder.bind(this))
    this.socket.on('successcloseOrder', this.handleSuccessCloseOrder.bind(this))

    this.socket.on('loadHistoryPeriodFast', this.handleLoadHistoryPeriodFast.bind(this))
    this.socket.on('updateHistoryNewFast', this.handleUpdateHistoryNewFast.bind(this))

    this.socket.on('updateAssets', this.handleUpdateAsset.bind(this))
    this.socket.on('updateCharts', this.handleUpdateCharts.bind(this))
    this.socket.on('updateStream', this.handleUpdateStream.bind(this))
    // this.socket.on('updateOpenedDeals', this.handleUpdateOpenedDeals.bind(this))
    // this.socket.on('updateClosedDeals', this.handleUpdateClosedDeals.bind(this))

    // this.socket.onAny((event) => {
    //   logger.info(`Received event: ${event}`)
    // })
  }

  protected handleConnect(): void {
    this.setState('connected')
    try {
      const auth = this.extractAuthData(this.ssID)

      if (!auth) {
        throw new Error('Failed to extract auth data')
      }

      const payload = this.createAuthPayload(auth)

      this.setState('authenticating')
      this.socket?.emit('auth', payload)
    } catch (error) {
      const message = this.getErrorMessage(error)
      logger.error(`Failed to authenticate with PocketOption: ${message}`)
    }
  }

  protected handleDisconnect(): void {
    this.setState('disconnected')
    this.disconnect()
  }

  protected handleError(error: Error): void {
    logger.error(`Socket error: ${error.message}`)
  }

  protected handleSuccessAuth(): void {
    this.setState('authenticated')
    logger.info('Successfully authenticated with PocketOption')

    this.startHeartbeat()
    this.broadcast('connected')
  }

  protected handleUpdateBalance(...args: unknown[]): void {
    const data = this.formatPayload(args) as Balance

    // Cache the balance
    this.accountBalance = data.balance
    this.broadcast('balance:updated', data)
  }

  protected handleSuccessOpenOrder(...args: unknown[]): void {
    const data = this.formatPayload(args) as OpenOrderResponse

    this.broadcast('trade:opened', data)
  }

  protected handleSuccessCloseOrder(...args: unknown[]): void {
    const data = this.formatPayload(args) as CloseOrderResponse

    // Process trade results with money management
    if (
      this.moneyManager &&
      this.moneyManager.isSessionActive() &&
      data.deals &&
      data.deals.length > 0
    ) {
      // Process each deal individually for money management
      data.deals.forEach((deal, index) => {
        const isWin = deal.profit > 0
        this.moneyManager!.placeTrade(isWin, deal.percentProfit)

        logger.info(
          `Trade result processed (${index + 1}/${data.deals.length}): ${isWin ? 'WIN' : 'LOSS'} - Profit: $${deal.profit.toFixed(2)} - Profit %: ${deal.percentProfit.toFixed(2)}`
        )
      })

      // Broadcast updated money management status after processing all deals
      const status = this.moneyManager.getSessionStatus()
      this.broadcast('money-management:status', status)

      // Check if session should end after processing all trades
      const stopCheck = this.moneyManager.shouldStopSession()
      if (stopCheck.shouldStop) {
        logger.warn(`Auto-stopping bot: ${stopCheck.reason}`)
        setTimeout(() => this.stopBot(), 1000) // Small delay to ensure trades are processed
      }
    }

    this.broadcast('trade:closed', data)
  }

  protected async handleLoadHistoryPeriodFast(...args: unknown[]): Promise<void> {
    const data = this.formatPayload(args) as HistoryPeriodFastData

    if (!this.signalEngine) return

    // sort ascending to build history in order
    const sorted = data.data.slice().sort((a, b) => a.time - b.time)
    for (const { time, open, high, low, close } of sorted) {
      const candle: Candle = { time, open, high, low, close }

      try {
        // This just populates the priceHistory data
        await this.signalEngine.generate(candle)
      } catch (err) {
        const message = this.getErrorMessage(err)
        logger.error(`Error seeding period history: ${message}`)
      }
    }

    logger.info(`🌱 Seeded ${sorted.length} historical candles for ${data.asset}`)
  }

  protected async handleUpdateHistoryNewFast(...args: unknown[]): Promise<void> {
    const data = this.formatPayload(args) as HistoryNewFastData

    if (!this.signalEngine) return

    const candleGroup = new Map<number, number[]>()

    const apiCandles: Candle[] = []

    for (const [time, open, high, low, close] of data.candles) {
      const candle: Candle = { time, open, high, low, close }

      apiCandles.push(candle)
    }

    // Sort the candles by time
    apiCandles.sort((a, b) => a.time - b.time)

    // Process the history data to build OHLC candles
    for (const [time, price] of data.history) {
      const timestamp = Math.floor(time / data.period) * data.period
      if (!candleGroup.has(timestamp)) {
        candleGroup.set(timestamp, [])
      }
      candleGroup.get(timestamp)!.push(price)
    }

    const ohlcCandles: Candle[] = []
    for (const [time, prices] of candleGroup) {
      if (prices.length > 0) {
        ohlcCandles.push({
          time,
          open: prices[0],
          high: Math.max(...prices),
          low: Math.min(...prices),
          close: prices[prices.length - 1]
        })
      }
    }

    // Remove the last candle since it's not closed yet
    if (ohlcCandles.length) {
      ohlcCandles.pop()
    }

    const combinedMap = new Map<number, Candle>()

    // API Candles have priority; add them first
    for (const candle of apiCandles) {
      combinedMap.set(candle.time, candle)
    }

    // Add tick-derived candles only if the timestamp is not already covered
    for (const candle of ohlcCandles) {
      if (!combinedMap.has(candle.time)) {
        combinedMap.set(candle.time, candle)
      }
    }

    const combinedCandles: Candle[] = Array.from(combinedMap.values())

    // Sort the combined candles by time
    combinedCandles.sort((a, b) => a.time - b.time)

    // Remove the last candle since it's not closed yet
    if (combinedCandles.length) {
      combinedCandles.pop()
    }

    for (const candle of combinedCandles) {
      try {
        await this.signalEngine.generate(candle)
      } catch (err) {
        const message = this.getErrorMessage(err)
        logger.error(`Error seeding newFast candles: ${message}`)
      }
    }

    logger.info(`🔄 Added ${combinedCandles.length} new-fast candles for ${data.asset}`)
  }

  protected handleUpdateAsset(...args: unknown[]): void {
    const data = formatData(args)

    if (!data) return

    const assets = formatAssets(data)

    const otcAssets = assets.filter(
      (asset) => asset.isOTC === 1 && asset.profit > 90 && asset.isActive
    )
    const noneOtcAssets = assets.filter((asset) => asset.isOTC === 0 && asset.isActive)

    this.broadcast('assets', {
      otcAssets,
      noneOtcAssets
    })
  }

  protected handleUpdateCharts(...args: unknown[]): void {
    const data = this.formatPayload(args) as ChartsData

    if (data.settings) {
      const chartSettings =
        typeof data.settings === 'string' ? JSON.parse(data.settings) : data.settings
      const finalData: ChartInfo = {
        chartID: data.chart_id,
        settings: chartSettings
      }

      this.chartSettings = finalData
    }
  }

  // Real-time data stream
  protected async handleUpdateStream(...args: unknown[]): Promise<void> {
    const data = this.formatPayload(args) as StreamData
    const [assetSymbol, timestamp, price] = data

    if (!this.signalEngine || !this.chartSettings || !this.tradeSettings) return

    // 1. derive expiry (in seconds) from your active chartPeriod
    const periodName = formatChartTimeframe(this.chartSettings.settings.chartPeriod)
    const expirySec = expiryToSeconds(periodName)

    // 2. bucket boundary
    const bucketTime = Math.floor(timestamp / expirySec) * expirySec
    let bucket = this.buckets.get(assetSymbol)

    // 3. new candle interval?
    if (!bucket || bucket.time !== bucketTime) {
      // ---- candle closed ----
      if (bucket) {
        const closedCandle: Candle = {
          time: bucket.time,
          open: bucket.open,
          high: bucket.high,
          low: bucket.low,
          close: bucket.close
        }

        const timestampMs = toMs(bucket.time)
        const dt = new Date(timestampMs)
        logger.debug(`bar closed @ ${dt.toISOString()}`)
        logger.debug(
          `Closed candle: O:${closedCandle.open} H:${closedCandle.high} L:${closedCandle.low} C:${closedCandle.close}`
        )

        // Generate Signal
        const signals = await this.signalEngine.generate(closedCandle)

        if (signals.length) {
          const bestSignal = this.signalEngine.getBestSignal()

          if (bestSignal.shouldTrade) {
            this.lastSignalBarTime = bucket.time
            if (this.isBotRunning) {
              if (this.moneyManager) {
                this.moneyManager.openTrade()
                const status = this.moneyManager.getSessionStatus()
                this.broadcast('money-management:status', status)
              }
              this.placeOrder(bestSignal)
            }

            logger.success(
              `Best signal: ${bestSignal.action} @ ${closedCandle.close} - ${bestSignal.reason}`
            )
          } else {
            logger.warn(`${bestSignal.action} @ ${closedCandle.close} - ${bestSignal.reason}`)
          }
        }
      }

      // ---- new candle ----
      bucket = {
        time: bucketTime,
        open: price,
        high: price,
        low: price,
        close: price
      }
    } else {
      // same interval → update highs/lows and latest close
      bucket.high = Math.max(bucket.high, price)
      bucket.low = Math.min(bucket.low, price)
      bucket.close = price

      // 👉 Real-time wick capture: re-run strategy on the in-flight candle
      const liveCandle: Candle = { ...bucket }
      if (bucket.time !== this.lastSignalBarTime) {
        const signals = await this.signalEngine.generate(liveCandle)
        signals.forEach((signal) => {
          if (signal.shouldTrade && bucket && signal.confidence > 0.6) {
            // Only high-confidence real-time signals
            this.lastSignalBarTime = bucket.time
            if (this.isBotRunning) {
              if (this.moneyManager) {
                this.moneyManager.openTrade()
                const status = this.moneyManager.getSessionStatus()
                this.broadcast('money-management:status', status)
              }
              this.placeOrder(signal)
              logger.debug(`[Real-time] Placed order: ${signal.action} - ${signal.reason}`)
            }
            signal.shouldTrade &&
              logger.info(
                `[Real-time] Signal generated: ${signal.action} @ ${bucket!.close} - ${signal.reason}`
              )
          }
        })
      }
    }

    this.buckets.set(assetSymbol, bucket)
  }

  protected startHeartbeat(): void {
    this.stopHeartbeat()

    if (this.socket && this.isConnected()) {
      this.heartbeatInterval = setInterval(() => {
        this.socket!.emit('ps')
      }, 20000)
    }
  }

  protected stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  public setState(state: BrokerState): void {
    this.state = state
    this.broadcastStateChange()
  }

  public getState(): BrokerState {
    return this.state
  }

  public getMoneyManagementStatus(): MoneyManagementStatus | null {
    return this.moneyManager?.getSessionStatus() || null
  }

  public getAccountBalance(): number {
    return this.accountBalance
  }

  private isConnected(): boolean {
    return this.state === 'connected' || this.state === 'authenticated'
  }

  private extractAuthData(ssID: string): { session: string; uid: number } | null {
    const auth = extractAuth(ssID)

    if (!auth || !auth.session || !auth.uid) {
      logger.error(`Invalid auth data: ${JSON.stringify(auth)}`)
      return null
    }

    return auth
  }

  private async loadHistoryPeriod(): Promise<void> {
    try {
      if (!this.socket) {
        throw new Error('Socket not connected')
      }

      if (!this.chartSettings) {
        throw new Error('Chart settings not set')
      }

      const chartSettings = this.chartSettings.settings

      const timeframe = formatChartTimeframe(chartSettings.chartPeriod)
      const period = expiryToSeconds(timeframe)
      const offset = timeframeToOffset(timeframe)
      const symbol = chartSettings.symbol

      const rand = Math.floor(Math.random() * 90 + 10).toString() // Random number between 10-99 (10-999)
      const cu = Math.floor(Date.now() / 1000) // Current Unix time in seconds
      const t = (cu + 2 * 60 * 60).toString() // Add 2 hours
      const index = parseInt(t + rand, 10) // Concatenate and convert to int

      this.socket.emit('loadHistoryPeriod', {
        asset: symbol,
        index,
        offset,
        period,
        time: Math.floor(Date.now() / 1000) // time size sample:if interval set 1 mean get time 0~1 candle
      })
    } catch (error) {
      const message = this.getErrorMessage(error)
      logger.error(`Failed to load history period: ${message}`)
    }
  }

  private createAuthPayload(auth: { session: string; uid: number }): {
    isDemo: number
    isFastHistory: boolean
    platform: number
    session: string
    uid: number
  } {
    return {
      isDemo: this.isDemo ? 1 : 0,
      isFastHistory: true,
      platform: 2,
      session: auth.session,
      uid: auth.uid
    }
  }

  private getErrorMessage(error: unknown): string {
    return error instanceof Error ? error.message : 'Unknown error'
  }

  private broadcast(event: string, data?: unknown): void {
    try {
      const windows = this.getActiveWindows()

      if (windows.length === 0) {
        logger.warn('No active windows for broadcast')
        return
      }

      const payload = this.formatPayload(data)

      this.sendToWindow(windows, event, payload)
    } catch (error) {
      const message = this.getErrorMessage(error)
      logger.error(`Failed to broadcast event: ${message}`)
    }
  }

  private getActiveWindows(): BrowserWindow[] {
    return BrowserWindow.getAllWindows().filter((w) => !w.isDestroyed())
  }

  private formatPayload(data?: unknown): unknown {
    if (!data) return data

    if (Array.isArray(data)) {
      const formatted = formatData(data)
      return Array.isArray(formatted) && formatted.length > 0 ? formatted[0] : formatted
    }

    return data
  }

  private sendToWindow(windows: BrowserWindow[], event: string, payload: unknown): void {
    windows.forEach((window) => {
      try {
        window.webContents.send('broker:event', event, payload)
      } catch (error) {
        const message = this.getErrorMessage(error)
        logger.error(`Failed to send event to window: ${message}`)
      }
    })
  }

  private broadcastStateChange(): void {
    this.broadcast('stateChanged', this.state)
  }

  static async cleanup(): Promise<void> {
    if (PocketOption.instance) {
      await PocketOption.instance.disconnect()
      PocketOption.instance = null
    }
  }
}
